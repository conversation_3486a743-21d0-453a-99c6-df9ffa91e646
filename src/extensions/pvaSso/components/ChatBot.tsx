import * as React from "react";
import * as ReactWeb<PERSON>hat from "botframework-webchat";
import { Spinner } from "office-ui-fabric-react/lib/Spinner";
import { useRef, useState, useEffect } from "react";
import { Dispatch } from "redux";

import { IChatbotProps } from "./IChatBotProps";
import MSAL<PERSON>rapper from "./MSALWrapper";

// Floating chat widget positioned bottom-right per design spec
// - Header with Close, Minimize, and "Open with Copilot" pill
// - Web Chat area with styled bubbles/colors
// - Minimized circular launcher with unread badge

const RADIUS = 12;
const shadow = "0 8px 24px rgba(0,0,0,0.12), 0 2px 6px rgba(0,0,0,0.08)";

const containerStyles: React.CSSProperties = {
  position: "fixed",
  right: 24,
  bottom: 24,
  background: "#F6F7F8",
  border: "1px solid #E5E7EB",
  boxShadow: shadow,
  borderRadius: RADIUS,
  display: "flex",
  flexDirection: "column",
  minWidth: 320,
  width: 520,
  maxWidth: 720,
  minHeight: 420,
  height: "70vh",
  zIndex: 2147483646,
};

const headerStyles: React.CSSProperties = {
  height: 48,
  display: "flex",
  alignItems: "center",
  padding: "0 12px",
  borderBottom: "1px solid #E5E7EB",
  gap: 8,
};

const iconBtn: React.CSSProperties = {
  width: 28,
  height: 28,
  display: "inline-flex",
  alignItems: "center",
  justifyContent: "center",
  borderRadius: "9999px",
  border: "1px solid #D1D5DB",
  background: "white",
  cursor: "pointer",
  fontSize: 14,
  lineHeight: 1,
};

const titleStyles: React.CSSProperties = {
  fontFamily: "-apple-system, Segoe UI, Roboto, Helvetica, Arial, sans-serif",
  fontSize: 15,
  fontWeight: 600,
  color: "#111827",
};

const pillBtn: React.CSSProperties = {
  marginLeft: "auto",
  padding: "6px 12px",
  background: "white",
  border: "1px solid #D1D5DB",
  borderRadius: 9999,
  color: "#111827",
  fontSize: 13,
  fontWeight: 600,
  cursor: "pointer",
};

const webchatWrap: React.CSSProperties = {
  flex: 1,
  background: "#FFFFFF",
  borderBottomLeftRadius: RADIUS,
  borderBottomRightRadius: RADIUS,
  overflow: "hidden",
  display: "flex",
};

const launcherBtnBase: React.CSSProperties = {
  position: "fixed",
  right: 24,
  bottom: 24,
  width: 56,
  height: 56,
  borderRadius: "50%",
  background: "#0078D4",
  color: "#FFFFFF",
  display: "flex",
  alignItems: "center",
  justifyContent: "center",
  boxShadow: shadow,
  cursor: "pointer",
  border: "none",
  zIndex: 2147483646,
};

const badgeStyles: React.CSSProperties = {
  position: "absolute",
  top: -4,
  right: -4,
  minWidth: 18,
  height: 18,
  padding: "0 4px",
  background: "#DC2626",
  color: "#FFFFFF",
  borderRadius: 9999,
  fontSize: 11,
  fontWeight: 700,
  display: "flex",
  alignItems: "center",
  justifyContent: "center",
};

export const PVAChatbotDialog: React.FunctionComponent<IChatbotProps> = (
  props
) => {
  const [isOpen, setIsOpen] = useState(false);
  const [isMinimized, setIsMinimized] = useState(true);
  const [unread, setUnread] = useState(0);
  const [isInitialized, setIsInitialized] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  // Persistent refs that maintain state across minimize/close cycles
  const directLineRef = useRef<any>(null);
  const storeRef = useRef<any>(null);
  const tokenRef = useRef<string | null>(null);

  // Your bot's token endpoint
  const botURL = props.botURL;

  // constructing URL using regional settings
  const environmentEndPoint = botURL.slice(
    0,
    botURL.indexOf("/powervirtualagents")
  );
  const apiVersion = botURL.slice(botURL.indexOf("api-version")).split("=")[1];
  const regionalChannelSettingsURL = `${environmentEndPoint}/powervirtualagents/regionalchannelsettings?api-version=${apiVersion}`;

  // Using refs instead of IDs to get the webchat and loading spinner elements
  const webChatRef = useRef<HTMLDivElement>(null);
  const loadingSpinnerRef = useRef<HTMLDivElement>(null);

  // Refs for unread count management
  const minimizedRef = useRef(isMinimized);
  const incrementUnreadRef = useRef(() => setUnread((prev) => prev + 1));

  // Update refs when state changes
  useEffect(() => {
    minimizedRef.current = isMinimized;
  }, [isMinimized]);

  useEffect(() => {
    incrementUnreadRef.current = () => setUnread((prev) => prev + 1);
  }, []);

  // A utility function that extracts the OAuthCard resource URI from the incoming activity or return undefined
  function getOAuthCardResourceUri(activity: any): string | undefined {
    const attachment = activity?.attachments?.[0];
    if (
      attachment?.contentType === "application/vnd.microsoft.card.oauth" &&
      attachment.content.tokenExchangeResource
    ) {
      return attachment.content.tokenExchangeResource.uri;
    }
  }

  const handleInit = async () => {
    // If already initialized, just render the existing WebChat
    if (
      isInitialized &&
      directLineRef.current &&
      storeRef.current &&
      tokenRef.current
    ) {
      renderWebChat();
      return;
    }

    // Prevent multiple simultaneous initializations
    if (isLoading) return;

    setIsLoading(true);

    try {
      const MSALWrapperInstance = new MSALWrapper(
        props.clientID,
        props.authority
      );

      // Trying to get token if user is already signed-in
      let responseToken = await MSALWrapperInstance.handleLoggedInUser(
        [props.customScope],
        props.userEmail
      );

      if (!responseToken) {
        // Trying to get token if user is not signed-in
        responseToken = await MSALWrapperInstance.acquireAccessToken(
          [props.customScope],
          props.userEmail
        );
      }

      const token = responseToken?.accessToken || null;
      tokenRef.current = token;

      // Get the regional channel URL
      let regionalChannelURL;

      const regionalResponse = await fetch(regionalChannelSettingsURL);
      if (regionalResponse.ok) {
        const data = await regionalResponse.json();
        regionalChannelURL = data.channelUrlsById.directline;
      } else {
        console.error(`HTTP error! Status: ${regionalResponse.status}`);
        return;
      }

      // Create DirectLine object
      const response = await fetch(botURL);

      if (response.ok) {
        const conversationInfo = await response.json();
        directLineRef.current = ReactWebChat.createDirectLine({
          token: conversationInfo.token,
          domain: regionalChannelURL + "v3/directline",
        });
      } else {
        console.error(`HTTP error! Status: ${response.status}`);
        return;
      }

      // Create store only if it doesn't exist
      if (!storeRef.current) {
        storeRef.current = ReactWebChat.createStore(
          {},
          ({ dispatch }: { dispatch: Dispatch }) =>
            (next: any) =>
            (action: any) => {
              if (
                props.greet &&
                action.type === "DIRECT_LINE/CONNECT_FULFILLED"
              ) {
                dispatch({
                  meta: { method: "keyboard" },
                  payload: {
                    activity: {
                      channelData: { postBack: true },
                      name: "startConversation",
                      type: "event",
                    },
                  },
                  type: "DIRECT_LINE/POST_ACTIVITY",
                });
                return next(action);
              }

              if (action.type === "DIRECT_LINE/INCOMING_ACTIVITY") {
                const activity = action.payload.activity;
                // Increment unread on bot message while minimized
                if (activity.from?.role === "bot" && minimizedRef.current) {
                  incrementUnreadRef.current();
                }
              }

              // Checking whether the bot is asking for authentication
              if (action.type === "DIRECT_LINE/INCOMING_ACTIVITY") {
                const activity = action.payload.activity;
                if (
                  activity.from &&
                  activity.from.role === "bot" &&
                  getOAuthCardResourceUri(activity)
                ) {
                  directLineRef.current
                    ?.postActivity({
                      type: "invoke",
                      name: "signin/tokenExchange",
                      value: {
                        id: activity.attachments[0].content
                          .tokenExchangeResource.id,
                        connectionName:
                          activity.attachments[0].content.connectionName,
                        token: tokenRef.current,
                      },
                      from: {
                        id: props.userEmail,
                        name: props.userFriendlyName,
                        role: "user",
                      },
                    })
                    .subscribe(
                      (id: any) => {
                        if (id === "retry") {
                          console.log(
                            "bot was not able to handle the invoke, so display the oauthCard"
                          );
                          return next(action);
                        }
                      },
                      () => {
                        console.log(
                          "An error occurred so display the oauthCard"
                        );
                        return next(action);
                      }
                    );
                  return;
                }
              }

              return next(action);
            }
        );
      }

      renderWebChat();
      setIsInitialized(true);
    } catch (error) {
      console.error("Failed to initialize chat:", error);
    } finally {
      setIsLoading(false);
    }
  };

  // Helper function to render WebChat with persistent state
  const renderWebChat = () => {
    if (!tokenRef.current || !directLineRef.current || !storeRef.current)
      return;

    // Style options per design
    const styleOptions: ReactWebChat.StyleOptions = {
      hideUploadButton: true,
      accent: "#0078D4",
      backgroundColor: "#FFFFFF",
      bubbleBackground: "#E6E5EB",
      bubbleTextColor: "#111827",
      bubbleBorderRadius: 12,
      bubbleBorderWidth: 0,
      bubbleFromUserBackground: "#0078D4",
      bubbleFromUserTextColor: "#FFFFFF",
      bubbleFromUserBorderWidth: 0,
      sendBoxBackground: "#FFFFFF",
      sendBoxBorderTop: "1px solid #E5E7EB",
      sendBoxBorderBottom: "1px solid #E5E7EB",
      sendBoxTextColor: "#111827",
      sendBoxHeight: 48,
      timestampColor: "#6B7280",
      paddingRegular: 16,
      subtle: "#4B5563",
    };

    // Render Web Chat
    if (webChatRef.current && loadingSpinnerRef.current) {
      loadingSpinnerRef.current.style.display = "none";
      webChatRef.current.style.flex = "1";
      ReactWebChat.renderWebChat(
        {
          directLine: directLineRef.current,
          store: storeRef.current,
          styleOptions,
          userID: props.userEmail,
        },
        webChatRef.current
      );
    } else {
      console.error("Webchat or loading spinner not found");
    }
  };

  // Initialize when first opened
  useEffect(() => {
    if (isOpen && !isInitialized) {
      handleInit().catch((error) => {
        console.error("Failed to initialize chat:", error);
      });
    }
    if (isOpen) {
      setUnread(0);
    }
  }, [isOpen, isInitialized]);

  // Event handlers that preserve state
  const openChat = () => {
    setIsOpen(true);
    setIsMinimized(false);
    setUnread(0);

    // If already initialized, just render the existing chat
    if (isInitialized && directLineRef.current && storeRef.current) {
      // Small delay to ensure DOM is ready
      setTimeout(() => {
        renderWebChat();
      }, 0);
    }
  };

  const minimizeChat = () => {
    setIsMinimized(true);
    setIsOpen(false);
    // Note: We do NOT reset initialization state - chat persists
  };

  const closeChat = () => {
    setIsMinimized(true);
    setIsOpen(false);
    // Note: We do NOT reset initialization state - chat persists
  };

  return (
    <>
      {/* Minimized launcher */}
      {isMinimized && (
        <button
          aria-label="Open chat"
          style={launcherBtnBase}
          onClick={openChat}
        >
          <span style={{ fontWeight: 700 }}>
            {props.botAvatarInitials || "LA"}
          </span>
          {unread > 0 && <span style={badgeStyles}>{unread}</span>}
        </button>
      )}

      {/* Floating chat container */}
      {isOpen && (
        <div
          role="dialog"
          aria-label={props.botName || "Learning Agent"}
          style={containerStyles}
        >
          <div style={headerStyles}>
            <button aria-label="Close chat" style={iconBtn} onClick={closeChat}>
              ×
            </button>
            <button
              aria-label="Minimize chat"
              style={iconBtn}
              onClick={minimizeChat}
            >
              –
            </button>
            <div style={{ width: 8 }} />
            <div style={titleStyles}>{props.botName || "Learning Agent"}</div>
            <a
              href="#"
              target="_blank"
              rel="noreferrer"
              aria-label="Open with Copilot (opens in new tab)"
              style={pillBtn}
            >
              Open with Copilot
            </a>
          </div>
          <div style={webchatWrap}>
            <div ref={webChatRef} role="main" style={{ flex: 1 }} />
            <div ref={loadingSpinnerRef}>
              <Spinner
                label="Loading..."
                style={{ paddingTop: "1rem", paddingBottom: "1rem" }}
              />
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default class Chatbot extends React.Component<IChatbotProps> {
  constructor(props: IChatbotProps) {
    super(props);
  }
  public render(): JSX.Element {
    return <PVAChatbotDialog {...this.props} />;
  }
}
