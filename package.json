{"name": "pva-extension-sso", "version": "0.0.1", "private": true, "engines": {"node": ">=16.13.0 <17.0.0 || >=18.17.1 <19.0.0"}, "main": "lib/index.js", "scripts": {"build": "gulp bundle", "clean": "gulp clean", "test": "gulp test"}, "dependencies": {"@microsoft/decorators": "1.18.0", "@microsoft/sp-application-base": "1.18.0", "@microsoft/sp-core-library": "1.18.0", "@microsoft/sp-dialog": "1.18.0", "@uifabric/react-hooks": "^7.16.4", "botframework-webchat": "^4.15.9", "office-ui-fabric-react": "^7.204.0", "p-defer-es5": "^2.0.1", "react": "^17.0.1", "react-dom": "^17.0.1", "tslib": "2.3.1"}, "devDependencies": {"@microsoft/eslint-config-spfx": "1.18.0", "@microsoft/eslint-plugin-spfx": "1.18.0", "@microsoft/rush-stack-compiler-4.7": "0.1.0", "@microsoft/sp-build-web": "1.18.0", "@microsoft/sp-module-interfaces": "1.18.0", "@rushstack/eslint-config": "2.5.1", "@types/webpack-env": "~1.15.2", "ajv": "^6.12.5", "eslint": "8.7.0", "gulp": "4.0.2", "typescript": "4.7.4", "babel-loader": "^8.3.0", "@babel/core": "^7.23.0", "@babel/preset-env": "^7.22.20"}}