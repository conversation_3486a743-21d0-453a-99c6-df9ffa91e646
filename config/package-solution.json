{"$schema": "https://developer.microsoft.com/json-schemas/spfx-build/package-solution.schema.json", "solution": {"name": "learning-agent-spfx-sso", "id": "14634225-91e5-41a4-b9cc-161ccb3400b4", "version": "*******", "includeClientSideAssets": true, "skipFeatureDeployment": true, "isDomainIsolated": false, "developer": {"name": "", "websiteUrl": "", "privacyUrl": "", "termsOfUseUrl": "", "mpnId": "Undefined-1.18.0"}, "metadata": {"shortDescription": {"default": "pva-extension-sso description"}, "longDescription": {"default": "pva-extension-sso description"}, "screenshotPaths": [], "videoUrl": "", "categories": []}, "features": [{"title": "Application Extension - Deployment of custom action", "description": "Deploys a custom action with ClientSideComponentId association", "id": "b9966a99-b9c1-4136-a847-d3e236c784a0", "version": "*******", "assets": {"elementManifests": ["elements.xml", "ClientSideInstance.xml"]}}]}, "paths": {"zippedPackage": "solution/learning-agent-spfx-sso.sppkg"}}